{"permissions": {"allow": ["Bash(npx react-native:*)", "Bash(java:*)", "Bash(echo $JAVA_HOME)", "Bash(/usr/libexec/java_home:*)", "Bash(echo $ANDROID_HOME)", "Bash(echo $ANDROID_SDK_ROOT)", "Bash($ANDROID_HOME/emulator/emulator:*)", "Bash(ls:*)", "Bash($ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager:*)", "Bash(system_profiler:*)", "Bash(\"/Applications/Android Studio.app/Contents/bin/studio.sh\" --help)", "<PERSON><PERSON>(ulimit:*)", "Bash(brew install:*)", "<PERSON><PERSON>(watchman watch:*)", "Bash(sysctl:*)", "Bash(find:*)", "<PERSON><PERSON>(csrutil:*)", "Bash(xcrun simctl list:*)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/Library/Android/sdk", "/Applications/Android Studio.app", "/Users/<USER>/.android/avd/Pixel_8_Pro.avd", "/Users/<USER>/.android/avd"]}}