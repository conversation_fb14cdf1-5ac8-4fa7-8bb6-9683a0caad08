{"permissions": {"allow": ["Bash(npx react-native:*)", "Bash(npx @react-native-community/cli init:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(java:*)", "Bash(echo $JAVA_HOME)", "Bash(JAVA_HOME=/Library/Java/JavaVirtualMachines/openjdk-17.jdk/Contents/Home ./gradlew assembleDebug)", "Bash(/usr/libexec/java_home:*)", "Bash(JAVA_HOME=/Library/Java/JavaVirtualMachines/temurin-17.jdk/Contents/Home ./gradlew assembleDebug)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/.gradle/wrapper/dists/gradle-8.8-all", "/Users/<USER>/.sdkman/candidates/java/8.0.352-zulu", "/Users/<USER>/Desktop/RNSplitDemo"]}}